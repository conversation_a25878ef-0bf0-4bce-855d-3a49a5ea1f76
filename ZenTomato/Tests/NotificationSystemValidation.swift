//
//  NotificationSystemValidation.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/16.
//  通知系统验证 - 验证需求3的所有验收标准
//

import Foundation
import UserNotifications

/// 通知系统验证器
class NotificationSystemValidator {
    
    private let notificationManager = NotificationManager()
    
    /// 验证所有需求3的验收标准
    func validateAllRequirements() -> ValidationResult {
        var results: [String: Bool] = [:]
        
        // 验收标准 3.1: 休息阶段开始时发送包含休息时长信息的通知
        results["3.1_break_start_notification"] = validateBreakStartNotification()
        
        // 验收标准 3.2: 休息阶段结束时发送提醒继续工作的通知
        results["3.2_break_end_notification"] = validateBreakEndNotification()
        
        // 验收标准 3.3: 应用首次使用时请求通知权限
        results["3.3_permission_request"] = validatePermissionRequest()
        
        // 验收标准 3.4: 通知权限被拒绝时提供系统设置快捷入口
        results["3.4_settings_access"] = validateSettingsAccess()
        
        // 验收标准 3.5: 应用在前台时仍然显示通知
        results["3.5_foreground_notifications"] = validateForegroundNotifications()
        
        // 验收标准 3.6: 用户点击通知时支持快速操作
        results["3.6_quick_actions"] = validateQuickActions()
        
        // 验收标准 3.7: 通知在通知中心保留历史记录
        results["3.7_notification_history"] = validateNotificationHistory()
        
        return ValidationResult(results: results)
    }
    
    // MARK: - 验证方法
    
    /// 验证休息开始通知
    private func validateBreakStartNotification() -> Bool {
        // 检查方法是否存在且可调用
        let duration: TimeInterval = 5 * 60 // 5分钟
        notificationManager.sendBreakStartNotification(duration: duration)
        
        // 验证本地化字符串
        let title = NSLocalizedString("notification.break_start.title", comment: "")
        let body = String(format: NSLocalizedString("notification.break_start.body", comment: ""), 5)
        
        return !title.isEmpty && body.contains("5")
    }
    
    /// 验证休息结束通知
    private func validateBreakEndNotification() -> Bool {
        notificationManager.sendBreakEndNotification()
        
        let title = NSLocalizedString("notification.break_end.title", comment: "")
        let body = NSLocalizedString("notification.break_end.body", comment: "")
        
        return !title.isEmpty && !body.isEmpty
    }
    
    /// 验证权限请求
    private func validatePermissionRequest() -> Bool {
        // 检查权限请求方法存在
        notificationManager.requestPermissionIfNeeded()
        
        // 验证权限状态属性存在
        let _ = notificationManager.authorizationStatus
        let _ = notificationManager.isAuthorized
        
        return true
    }
    
    /// 验证系统设置访问
    private func validateSettingsAccess() -> Bool {
        // 检查打开系统设置方法存在
        notificationManager.openSystemSettings()
        return true
    }
    
    /// 验证前台通知
    private func validateForegroundNotifications() -> Bool {
        // 检查UNUserNotificationCenterDelegate实现
        return notificationManager.conforms(to: UNUserNotificationCenterDelegate.self)
    }
    
    /// 验证快速操作
    private func validateQuickActions() -> Bool {
        // 验证通知动作本地化
        let skipAction = NSLocalizedString("notification.action.skip", comment: "")
        let startAction = NSLocalizedString("notification.action.start_now", comment: "")
        
        return !skipAction.isEmpty && !startAction.isEmpty
    }
    
    /// 验证通知历史记录
    private func validateNotificationHistory() -> Bool {
        // 检查清理方法存在
        notificationManager.cleanupOldNotifications()
        return true
    }
}

/// 验证结果
struct ValidationResult {
    let results: [String: Bool]
    
    var allPassed: Bool {
        return results.values.allSatisfy { $0 }
    }
    
    var passedCount: Int {
        return results.values.filter { $0 }.count
    }
    
    var totalCount: Int {
        return results.count
    }
    
    var summary: String {
        let passed = passedCount
        let total = totalCount
        let percentage = total > 0 ? (passed * 100 / total) : 0
        
        return """
        通知系统验证结果:
        通过: \(passed)/\(total) (\(percentage)%)
        
        详细结果:
        \(results.map { "- \($0.key): \($0.value ? "✅" : "❌")" }.joined(separator: "\n"))
        
        整体状态: \(allPassed ? "✅ 所有验收标准通过" : "❌ 部分验收标准未通过")
        """
    }
}

// MARK: - 使用示例

#if DEBUG
extension NotificationSystemValidator {
    /// 运行验证并打印结果
    static func runValidation() {
        let validator = NotificationSystemValidator()
        let result = validator.validateAllRequirements()
        print(result.summary)
    }
}
#endif
